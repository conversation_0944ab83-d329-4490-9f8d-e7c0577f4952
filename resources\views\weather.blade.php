<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'Lara<PERSON>') }} - Weather Forecast</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .weather-hero {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .weather-card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            transition: transform 0.3s ease;
        }
        .weather-card:hover {
            transform: translateY(-5px);
        }
        .weather-icon {
            width: 120px;
            height: 120px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
        }
        .forecast-card {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            transition: all 0.3s ease;
            border: none;
        }
        .forecast-card:hover {
            background: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .hourly-scroll {
            overflow-x: auto;
            white-space: nowrap;
            padding-bottom: 10px;
        }
        .hourly-item {
            display: inline-block;
            min-width: 100px;
            margin-right: 15px;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
        }
        .temp-display {
            font-size: 4rem;
            font-weight: 300;
            line-height: 1;
        }
        .navbar-custom {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#">
                <i class="bi bi-cloud-sun me-2"></i>{{ config('app.name', 'Laravel') }}
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="#">Weather</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">Profile</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-5 pt-5">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="display-4 fw-bold text-white mb-2">
                    <i class="bi bi-cloud-sun me-3"></i>Weather Forecast
                </h1>
                <p class="lead text-white-50">Stay updated with current weather conditions and forecasts</p>
            </div>
        </div>

        <!-- Current Weather Hero Section -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="weather-hero text-white p-5">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h2 class="display-5 fw-bold mb-2">{{ $currentWeather['city'] ?? 'New York' }}</h2>
                            <p class="fs-5 opacity-75 mb-4">{{ $currentWeather['date'] ?? date('l, F j, Y') }}</p>
                            <div class="temp-display">{{ $currentWeather['temperature'] ?? '22' }}°C</div>
                            <p class="fs-4 opacity-75">{{ $currentWeather['condition'] ?? 'Partly Cloudy' }}</p>
                        </div>
                        <div class="col-md-6 text-center">
                            <div class="weather-icon mb-4">
                                <i class="bi bi-sun display-1 text-warning"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Weather Details Grid -->
                    <div class="row g-3 mt-4">
                        <div class="col-6 col-md-3">
                            <div class="text-center p-3 rounded" style="background: rgba(255,255,255,0.2);">
                                <i class="bi bi-thermometer-half fs-4 mb-2"></i>
                                <p class="mb-1 opacity-75">Feels Like</p>
                                <h5 class="mb-0">{{ $currentWeather['feels_like'] ?? '25' }}°C</h5>
                            </div>
                        </div>
                        <div class="col-6 col-md-3">
                            <div class="text-center p-3 rounded" style="background: rgba(255,255,255,0.2);">
                                <i class="bi bi-droplet fs-4 mb-2"></i>
                                <p class="mb-1 opacity-75">Humidity</p>
                                <h5 class="mb-0">{{ $currentWeather['humidity'] ?? '65' }}%</h5>
                            </div>
                        </div>
                        <div class="col-6 col-md-3">
                            <div class="text-center p-3 rounded" style="background: rgba(255,255,255,0.2);">
                                <i class="bi bi-wind fs-4 mb-2"></i>
                                <p class="mb-1 opacity-75">Wind Speed</p>
                                <h5 class="mb-0">{{ $currentWeather['wind_speed'] ?? '12' }} km/h</h5>
                            </div>
                        </div>
                        <div class="col-6 col-md-3">
                            <div class="text-center p-3 rounded" style="background: rgba(255,255,255,0.2);">
                                <i class="bi bi-speedometer2 fs-4 mb-2"></i>
                                <p class="mb-1 opacity-75">Pressure</p>
                                <h5 class="mb-0">{{ $currentWeather['pressure'] ?? '1013' }} hPa</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Weather Information -->
        <div class="row g-4 mb-5">
            <!-- Weather Details Card -->
            <div class="col-md-6">
                <div class="card weather-card h-100">
                    <div class="card-body p-4">
                        <h5 class="card-title fw-bold mb-4">
                            <i class="bi bi-info-circle me-2 text-primary"></i>Additional Information
                        </h5>
                        <div class="row g-3">
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                    <span class="text-muted"><i class="bi bi-brightness-high me-2"></i>UV Index</span>
                                    <span class="fw-bold text-warning">{{ $currentWeather['uv_index'] ?? '6 (High)' }}</span>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                    <span class="text-muted"><i class="bi bi-eye me-2"></i>Visibility</span>
                                    <span class="fw-bold">{{ $currentWeather['visibility'] ?? '10 km' }}</span>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                    <span class="text-muted"><i class="bi bi-sunrise me-2"></i>Sunrise</span>
                                    <span class="fw-bold">{{ $currentWeather['sunrise'] ?? '6:30 AM' }}</span>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center py-2">
                                    <span class="text-muted"><i class="bi bi-sunset me-2"></i>Sunset</span>
                                    <span class="fw-bold">{{ $currentWeather['sunset'] ?? '7:45 PM' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Air Quality Card -->
            <div class="col-md-6">
                <div class="card weather-card h-100">
                    <div class="card-body p-4">
                        <h5 class="card-title fw-bold mb-4">
                            <i class="bi bi-lungs me-2 text-success"></i>Air Quality
                        </h5>
                        <div class="row g-3">
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                    <span class="text-muted">AQI</span>
                                    <span class="badge bg-success fs-6">{{ $currentWeather['aqi'] ?? '42 Good' }}</span>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                    <span class="text-muted">PM2.5</span>
                                    <span class="fw-bold">{{ $currentWeather['pm25'] ?? '15 μg/m³' }}</span>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                    <span class="text-muted">PM10</span>
                                    <span class="fw-bold">{{ $currentWeather['pm10'] ?? '25 μg/m³' }}</span>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center py-2">
                                    <span class="text-muted">Ozone</span>
                                    <span class="fw-bold">{{ $currentWeather['ozone'] ?? '65 μg/m³' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 5-Day Forecast -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card weather-card">
                    <div class="card-body p-4">
                        <h5 class="card-title fw-bold mb-4">
                            <i class="bi bi-calendar-week me-2 text-primary"></i>5-Day Forecast
                        </h5>
                        <div class="row g-3">
                            @php
                                $forecast = $weatherForecast ?? [
                                    ['day' => 'Today', 'high' => '25', 'low' => '18', 'condition' => 'Sunny', 'icon' => 'bi-sun'],
                                    ['day' => 'Tomorrow', 'high' => '23', 'low' => '16', 'condition' => 'Cloudy', 'icon' => 'bi-cloud'],
                                    ['day' => 'Wednesday', 'high' => '21', 'low' => '14', 'condition' => 'Rainy', 'icon' => 'bi-cloud-rain'],
                                    ['day' => 'Thursday', 'high' => '24', 'low' => '17', 'condition' => 'Partly Cloudy', 'icon' => 'bi-cloud-sun'],
                                    ['day' => 'Friday', 'high' => '26', 'low' => '19', 'condition' => 'Sunny', 'icon' => 'bi-sun']
                                ];
                            @endphp

                            @foreach($forecast as $day)
                            <div class="col-lg-2 col-md-4 col-6">
                                <div class="forecast-card p-3 text-center">
                                    <h6 class="fw-bold mb-3">{{ $day['day'] }}</h6>

                                    <!-- Weather Icon -->
                                    <div class="mb-3">
                                        @if($day['icon'] == 'bi-sun')
                                            <i class="bi bi-sun display-6 text-warning"></i>
                                        @elseif($day['icon'] == 'bi-cloud')
                                            <i class="bi bi-cloud display-6 text-secondary"></i>
                                        @elseif($day['icon'] == 'bi-cloud-rain')
                                            <i class="bi bi-cloud-rain display-6 text-primary"></i>
                                        @elseif($day['icon'] == 'bi-cloud-sun')
                                            <i class="bi bi-cloud-sun display-6 text-info"></i>
                                        @else
                                            <i class="bi bi-sun display-6 text-warning"></i>
                                        @endif
                                    </div>

                                    <p class="small text-muted mb-2">{{ $day['condition'] }}</p>
                                    <div class="d-flex justify-content-between">
                                        <span class="fw-bold">{{ $day['high'] }}°</span>
                                        <span class="text-muted">{{ $day['low'] }}°</span>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hourly Forecast -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card weather-card">
                    <div class="card-body p-4">
                        <h5 class="card-title fw-bold mb-4">
                            <i class="bi bi-clock me-2 text-primary"></i>Hourly Forecast
                        </h5>
                        <div class="hourly-scroll">
                            @php
                                $hourlyForecast = $hourlyWeather ?? [
                                    ['time' => '12 PM', 'temp' => '22', 'condition' => 'Sunny', 'icon' => 'bi-sun'],
                                    ['time' => '1 PM', 'temp' => '24', 'condition' => 'Sunny', 'icon' => 'bi-sun'],
                                    ['time' => '2 PM', 'temp' => '25', 'condition' => 'Partly Cloudy', 'icon' => 'bi-cloud-sun'],
                                    ['time' => '3 PM', 'temp' => '26', 'condition' => 'Partly Cloudy', 'icon' => 'bi-cloud-sun'],
                                    ['time' => '4 PM', 'temp' => '25', 'condition' => 'Cloudy', 'icon' => 'bi-cloud'],
                                    ['time' => '5 PM', 'temp' => '23', 'condition' => 'Cloudy', 'icon' => 'bi-cloud'],
                                    ['time' => '6 PM', 'temp' => '21', 'condition' => 'Cloudy', 'icon' => 'bi-cloud'],
                                    ['time' => '7 PM', 'temp' => '20', 'condition' => 'Clear', 'icon' => 'bi-moon-stars']
                                ];
                            @endphp

                            @foreach($hourlyForecast as $hour)
                            <div class="hourly-item">
                                <p class="small text-muted mb-2">{{ $hour['time'] }}</p>
                                <div class="mb-2">
                                    @if($hour['icon'] == 'bi-sun')
                                        <i class="bi bi-sun fs-4 text-warning"></i>
                                    @elseif($hour['icon'] == 'bi-cloud')
                                        <i class="bi bi-cloud fs-4 text-secondary"></i>
                                    @elseif($hour['icon'] == 'bi-cloud-sun')
                                        <i class="bi bi-cloud-sun fs-4 text-info"></i>
                                    @elseif($hour['icon'] == 'bi-moon-stars')
                                        <i class="bi bi-moon-stars fs-4 text-dark"></i>
                                    @else
                                        <i class="bi bi-sun fs-4 text-warning"></i>
                                    @endif
                                </div>
                                <p class="fw-bold mb-0">{{ $hour['temp'] }}°C</p>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
