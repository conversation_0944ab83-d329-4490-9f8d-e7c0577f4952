<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Weather Forecast') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Current Weather Card -->
            <div class="bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 overflow-hidden shadow-xl sm:rounded-lg mb-8">
                <div class="p-8 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-4xl font-bold mb-2">{{ $currentWeather['city'] ?? 'New York' }}</h1>
                            <p class="text-blue-100 text-lg">{{ $currentWeather['date'] ?? date('l, F j, Y') }}</p>
                        </div>
                        <div class="text-right">
                            <div class="text-6xl font-light">{{ $currentWeather['temperature'] ?? '22' }}°</div>
                            <p class="text-blue-100 text-lg">{{ $currentWeather['condition'] ?? 'Partly Cloudy' }}</p>
                        </div>
                    </div>
                    
                    <!-- Weather Icon -->
                    <div class="flex items-center justify-center my-8">
                        <div class="w-32 h-32 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                            <svg class="w-20 h-20 text-yellow-300" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- Weather Details -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
                        <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                            <p class="text-blue-100 text-sm">Feels Like</p>
                            <p class="text-2xl font-semibold">{{ $currentWeather['feels_like'] ?? '25' }}°</p>
                        </div>
                        <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                            <p class="text-blue-100 text-sm">Humidity</p>
                            <p class="text-2xl font-semibold">{{ $currentWeather['humidity'] ?? '65' }}%</p>
                        </div>
                        <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                            <p class="text-blue-100 text-sm">Wind Speed</p>
                            <p class="text-2xl font-semibold">{{ $currentWeather['wind_speed'] ?? '12' }} km/h</p>
                        </div>
                        <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                            <p class="text-blue-100 text-sm">Pressure</p>
                            <p class="text-2xl font-semibold">{{ $currentWeather['pressure'] ?? '1013' }} hPa</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Weather Info -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <!-- UV Index & Visibility -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Additional Information</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">UV Index</span>
                                <span class="font-semibold text-orange-500">{{ $currentWeather['uv_index'] ?? '6 (High)' }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Visibility</span>
                                <span class="font-semibold">{{ $currentWeather['visibility'] ?? '10 km' }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Sunrise</span>
                                <span class="font-semibold">{{ $currentWeather['sunrise'] ?? '6:30 AM' }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Sunset</span>
                                <span class="font-semibold">{{ $currentWeather['sunset'] ?? '7:45 PM' }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Air Quality -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Air Quality</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">AQI</span>
                                <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-semibold">
                                    {{ $currentWeather['aqi'] ?? '42 Good' }}
                                </span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">PM2.5</span>
                                <span class="font-semibold">{{ $currentWeather['pm25'] ?? '15 μg/m³' }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">PM10</span>
                                <span class="font-semibold">{{ $currentWeather['pm10'] ?? '25 μg/m³' }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Ozone</span>
                                <span class="font-semibold">{{ $currentWeather['ozone'] ?? '65 μg/m³' }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 5-Day Forecast -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6">5-Day Forecast</h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
                        @php
                            $forecast = $weatherForecast ?? [
                                ['day' => 'Today', 'high' => '25', 'low' => '18', 'condition' => 'Sunny', 'icon' => 'sun'],
                                ['day' => 'Tomorrow', 'high' => '23', 'low' => '16', 'condition' => 'Cloudy', 'icon' => 'cloud'],
                                ['day' => 'Wednesday', 'high' => '21', 'low' => '14', 'condition' => 'Rainy', 'icon' => 'rain'],
                                ['day' => 'Thursday', 'high' => '24', 'low' => '17', 'condition' => 'Partly Cloudy', 'icon' => 'partly-cloudy'],
                                ['day' => 'Friday', 'high' => '26', 'low' => '19', 'condition' => 'Sunny', 'icon' => 'sun']
                            ];
                        @endphp

                        @foreach($forecast as $day)
                        <div class="bg-gray-50 rounded-lg p-4 text-center hover:bg-gray-100 transition-colors">
                            <p class="font-semibold text-gray-800 mb-2">{{ $day['day'] }}</p>
                            
                            <!-- Weather Icon Placeholder -->
                            <div class="w-12 h-12 mx-auto mb-3 bg-blue-100 rounded-full flex items-center justify-center">
                                @if($day['icon'] == 'sun')
                                    <svg class="w-8 h-8 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                                    </svg>
                                @elseif($day['icon'] == 'cloud')
                                    <svg class="w-8 h-8 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M5.5 16a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 16h-8z"></path>
                                    </svg>
                                @elseif($day['icon'] == 'rain')
                                    <svg class="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M5.5 16a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 16h-8z"></path>
                                    </svg>
                                @else
                                    <svg class="w-8 h-8 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                                    </svg>
                                @endif
                            </div>
                            
                            <p class="text-sm text-gray-600 mb-2">{{ $day['condition'] }}</p>
                            <div class="flex justify-between text-sm">
                                <span class="font-semibold">{{ $day['high'] }}°</span>
                                <span class="text-gray-500">{{ $day['low'] }}°</span>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Hourly Forecast -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mt-8">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6">Hourly Forecast</h3>
                    <div class="flex overflow-x-auto space-x-4 pb-4">
                        @php
                            $hourlyForecast = $hourlyWeather ?? [
                                ['time' => '12 PM', 'temp' => '22', 'condition' => 'Sunny'],
                                ['time' => '1 PM', 'temp' => '24', 'condition' => 'Sunny'],
                                ['time' => '2 PM', 'temp' => '25', 'condition' => 'Partly Cloudy'],
                                ['time' => '3 PM', 'temp' => '26', 'condition' => 'Partly Cloudy'],
                                ['time' => '4 PM', 'temp' => '25', 'condition' => 'Cloudy'],
                                ['time' => '5 PM', 'temp' => '23', 'condition' => 'Cloudy'],
                                ['time' => '6 PM', 'temp' => '21', 'condition' => 'Cloudy'],
                                ['time' => '7 PM', 'temp' => '20', 'condition' => 'Clear']
                            ];
                        @endphp

                        @foreach($hourlyForecast as $hour)
                        <div class="flex-shrink-0 bg-gray-50 rounded-lg p-3 text-center min-w-[80px]">
                            <p class="text-sm text-gray-600 mb-2">{{ $hour['time'] }}</p>
                            <div class="w-8 h-8 mx-auto mb-2 bg-blue-100 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <p class="font-semibold text-gray-800">{{ $hour['temp'] }}°</p>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
