<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class WeatherController extends Controller
{
    protected $baseUrl;
    protected $apiKey;

    public function __construct(){
        $this->baseUrl = 'https://api.openweathermap.org/data/2.5';
        $this->apiKey = config('services.openweathermap.key');
    }

    public function getWeatherData(Request $request){
        $city = $request->input('city');
        $url = "{$this->baseUrl}/weather?q={$city}&appid={$this->apiKey}&units=celsius";
        $response = Http::get($url);
        $weatherData = $response->json();
        return view('weather', compact('weatherData'));
    }
}

